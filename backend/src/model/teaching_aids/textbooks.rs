use std::collections::HashMap;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Textbook {
    pub id: Uuid,
    pub title: String,
    pub subject_id: Option<Uuid>,
    pub grade_level_id: Option<Uuid>,
    pub publisher: Option<String>,
    pub publication_year: Option<i32>,
    #[serde(rename = "coverPath")]
    pub cover_path: Option<String>,
    pub isbn: Option<String>,
    pub version: Option<String>,
    pub status: Option<String>,
    pub creator_id: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TextbookVO {
    pub id: Uuid,
    pub title: String,
    pub subject_id: Option<Uuid>,
    pub subject: Option<String>,
    pub grade_level_id: Option<Uuid>,
    pub grade_level: Option<String>,
    pub publisher: Option<String>,
    pub publication_year: Option<i32>,
    #[serde(rename = "coverPath")]
    pub cover_path: Option<String>,
    pub isbn: Option<String>,
    pub version: Option<String>,
    pub status: Option<String>,
    pub creator_id: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// Textbook -> TextbookVO 的转换
impl From<Textbook> for TextbookVO {
    fn from(textbook: Textbook) -> Self {
        Self {
            id: textbook.id,
            title: textbook.title,
            subject_id: textbook.subject_id,
            subject: None,
            grade_level_id: textbook.grade_level_id,
            grade_level: None,
            publisher: textbook.publisher,
            publication_year: textbook.publication_year,
            cover_path: textbook.cover_path,
            isbn: textbook.isbn,
            version: textbook.version,
            status: textbook.status,
            creator_id: textbook.creator_id,
            created_at: textbook.created_at,
            updated_at: textbook.updated_at,
        }
    }
}

/// TextbookVO -> Textbook 的转换
impl From<TextbookVO> for Textbook {
    fn from(vo: TextbookVO) -> Self {
        Self {
            id: vo.id,
            title: vo.title,
            subject_id: vo.subject_id,
            grade_level_id: vo.grade_level_id,
            publisher: vo.publisher,
            publication_year: vo.publication_year,
            cover_path: vo.cover_path,
            isbn: vo.isbn,
            version: vo.version,
            status: vo.status,
            creator_id: vo.creator_id,
            created_at: vo.created_at,
            updated_at: vo.updated_at,
        }
    }
}

/// 可选：提供带 subject 和 grade_level 的构造函数
impl TextbookVO {
    pub fn with_details(
        textbook: Textbook,
        subject: Option<String>,
        grade_level: Option<String>,
    ) -> Self {
        let mut vo = Self::from(textbook);
        vo.subject = subject;
        vo.grade_level = grade_level;
        vo
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TextbookTenantAccess {
    pub id: Uuid,
    pub textbook_id: Uuid,
    pub tenant_id: Uuid,
    pub granted_by: Uuid,
    pub created_at: DateTime<Utc>,
}

// Request/Response DTOs for Teaching Aids
#[derive(Debug, Deserialize)]
pub struct CreateTeachingAidRequest {
    pub title: String,
    pub description: Option<String>,
    pub author: Option<String>,
    pub publisher: Option<String>,
    pub subject: Option<Uuid>,
    pub grade_level: Option<Uuid>,
    pub version: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateTeachingAidRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub author: Option<String>,
    pub publisher: Option<String>,
    pub version: Option<String>,
    pub subject: Option<Uuid>,
    pub grade_level: Option<Uuid>,
    pub status: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportTeachingAidRequest {
    pub title: String,
    pub description: Option<String>,
    pub author: Option<String>,
    pub publisher: Option<String>,
    pub subject_id: Option<Uuid>,
    pub grade_level_id: Option<Uuid>,
    pub version: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct TeachingAidQuery {
    pub page: Option<i64>,
    pub page_size: Option<i64>,
    pub subject_id: Option<Uuid>,
    pub grade_level_id: Option<Uuid>,
    pub status: Option<String>,
    pub search: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct RecentTeachingAidsQuery {
    pub limit: Option<i64>,
    pub sort: Option<String>,
    pub order: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreateChapterRequest {
    pub chapter_number: i32,
    pub title: String,
    pub content: Option<String>,
    pub knowledge_points: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct CreateAuthorizationRequest {
    pub textbook_id: Uuid,
    pub tenant_id: Uuid,
}

// Response DTOs
#[derive(Debug, Serialize)]
pub struct TeachingAid {
    pub id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub author: Option<String>,
    pub publisher: Option<String>,
    pub subject: Option<String>,
    pub grade_level: Option<String>,
    pub version: Option<String>,
    pub status: Option<String>,
    #[serde(rename = "coverPath")]
    pub cover_path: Option<String>,
    pub creator_id: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct TeachingAidChapter {
    pub id: Uuid,
    pub textbook_id: Uuid,
    pub chapter_number: i32,
    pub title: String,
    pub content: Option<String>,
    pub knowledge_points: Option<Value>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct TeachingAidChapter2 {
    pub id: Uuid,
    pub textbook_id: Uuid,
    pub parent_id: Option<Uuid>,
    pub chapter_number: i32,
    pub title: String,
    pub description: Option<String>,
    pub content: Option<Value>,
    pub metadata: Option<Value>,
    pub creator_id: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct TeachingAidPopularSubject {
    pub subject: String,
    pub count: i64,
}

#[derive(Debug, Serialize)]
pub struct TeachingAidRecentImport {
    pub id: String,
    pub title: String,
    pub status: String,
    pub count: i64,
    pub imported_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct TeachingAidStatsResponse {
    pub total_textbooks: i64,
    pub total_chapters: i64,
    pub total_exercises: i64,
    pub total_authorized_tenants: i64,
    pub popular_subjects: Option<Vec<TeachingAidPopularSubject>>,
    pub recent_imports: Option<Vec<TeachingAidRecentImport>>,
}

#[derive(Debug, Serialize)]
pub struct TeachingAidAuthorizationResponse {
    pub id: Uuid,
    pub textbook_id: Uuid,
    pub tenant_id: Uuid,
    pub granted_by: Uuid,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct ImportProgressResponse {
    pub id: Uuid,
    pub filename: String,
    pub status: String,
    pub total_records: i32,
    pub processed_records: i32,
    pub success_count: i32,
    pub error_count: i32,
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
}

/// 教辅导入记录
#[derive(Debug, Serialize)]
pub struct TextbookImportLog {
    pub id: Uuid,
    pub filename: String,
    // 导入状态
    pub status: String,
    pub total_chapters: i32,
    pub total_exercises: i32,
    pub started_at: DateTime<Utc>,
    pub completed_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

#[derive(Deserialize,Debug)]
pub struct TextbookJson {
    pub title: String,
    pub subject: String,
    pub grade: String,
    pub publisher: Option<String>,
    pub publish_year: Option<i32>,
    #[serde(rename = "coverPath")]
    pub cover_path: Option<String>,
    pub isbn: Option<String>,
}

#[derive(Deserialize, Serialize)]
pub struct ChapterJson {
    pub id: String,
    pub title: String,
    pub sequence: i32,
    pub content: ChapterContentJson,
    #[serde(rename = "parentId")]
    pub parent_id: Option<String>,
    pub metadata: Option<Value>,
}

#[derive(Deserialize, Serialize)]
pub struct ChapterContentJson {
    #[serde(rename = "type")]
    pub _type: Option<String>,
    pub attrs: Option<HashMap<String, Value>>,
    pub marks: Option<Vec<Value>>,
    pub content: Option<Vec<ChapterContentJson>>,
    pub text: Option<String>,
}


#[derive(Deserialize, Serialize, Debug)]
pub struct BookJson {
    pub id: Uuid, // 用 Uuid 类型
    pub title: String,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "gradeLevelCode")]
    pub grade_level_code: String,
    pub publisher: Option<String>,
    pub distributor: Option<String>,
    pub year: i16,
    #[serde(rename = "coverPath")]
    pub cover_path: Option<String>,
    pub isbn: String,
    pub edition: String,
    #[serde(rename = "printingVersion")]
    pub printing_version: Option<String>,
    pub authors: Option<Vec<String>>,
    pub summary: Option<String>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AnswerJson {
    pub id: Uuid,
    #[serde(rename = "questionId")]
    pub question_id: Uuid,
    #[serde(rename = "answerAreaId")]
    pub answer_area_id: i16,
    pub content: String,
    pub explanation: Option<String>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CatalogJson {
    pub id: Uuid,
    #[serde(rename = "bookId")]
    pub book_id: Uuid,
    #[serde(rename = "parentId")]
    pub parent_id: Option<Uuid>,
    pub serial: i16,
    pub level: i16,
    pub title: String,
    #[serde(rename = "sectionId")]
    pub section_id: Option<Uuid>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<String>, // 可替换成 Option<DateTime<Utc>>
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QuestionJson {
    pub id: Uuid,
    #[serde(rename = "questionTypeCode")]
    pub question_type_code: String,
    pub items: Vec<Value>,
    #[serde(rename = "updatedAt")]
    pub updated_at: String,
    #[serde(rename = "subjectCode")]
    pub subject_code:Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SectionJson {
    pub id: Uuid,
    #[serde(rename = "answerCardId")]
    pub answer_card_id: Option<Uuid>,
    pub snapshots: Vec<String>,
    pub items: Vec<Value>,
}