use crate::model::user::auth::AuthResult;
use crate::repository::user::identities::user_identity_repository::UserIdentityRepository;
use crate::repository::user::parent::ParentRepository;
use crate::service::auth::auth_service::AuthService;
use crate::service::classes::classes_service::ClassesService;
use crate::service::sms::SmsService;
use crate::service::tenant::TenantService;
use crate::service::user::identity_service::IdentityService;
use crate::service::user::parent_service::ParentService;
use crate::utils::password::PasswordService;
use sqlx::PgPool;
use std::sync::Arc;

pub struct AuthIntegration {
    pub auth_service: Arc<AuthService>,
    pub identity_service: Arc<IdentityService>,
    pub parent_service: Arc<ParentService>,
    pub sms_service: Arc<SmsService>,
    pub password_service: Arc<PasswordService>,
    pub tenant_service: Arc<TenantService>,
    pub classes_service: Arc<ClassesService>,
}

impl AuthIntegration {
    pub fn new(db: PgPool) -> AuthResult<Self> {
        // Initialize core services
        let sms_service = Arc::new(SmsService::new());
        let password_service = Arc::new(PasswordService::new());

        // Initialize business services
        let auth_service = Arc::new(AuthService::new(
            db.clone(),
            sms_service.clone(),
            password_service.clone(),
        ));

        // Initialize repository and identity service
        let user_identity_repository = Arc::new(UserIdentityRepository::new(db.clone()));
        let identity_service = Arc::new(IdentityService::new(db.clone(), user_identity_repository));

        // Initialize parent repository and service
        let parent_repository = Arc::new(ParentRepository::new(db.clone()));
        let parent_service = Arc::new(ParentService::new(db.clone(), parent_repository));

        let tenant_service = Arc::new(TenantService::new(db.clone()));
        let classes_service = Arc::new(ClassesService::new(db.clone()));
        Ok(Self {
            auth_service,
            identity_service,
            parent_service,
            sms_service,
            password_service,
            tenant_service,
            classes_service,
        })
    }

    pub fn auth_service(&self) -> Arc<AuthService> {
        self.auth_service.clone()
    }

    pub fn identity_service(&self) -> Arc<IdentityService> {
        self.identity_service.clone()
    }

    pub fn parent_service(&self) -> Arc<ParentService> {
        self.parent_service.clone()
    }
    pub fn tenant_service(&self) -> Arc<TenantService> {
        self.tenant_service.clone()
    }
    pub fn classes_service(&self) -> Arc<ClassesService> {
        self.classes_service.clone()
    }
    
    pub fn password_service(&self) -> Arc<PasswordService> {
        self.password_service.clone()
    }
}
