use crate::service::paper::paper::PaperService;
use crate::service::teaching_aids::textbook_paper_service::TextbookPaperService;
use std::sync::Arc;

use crate::{
    middleware::auth_middleware::AuthExtractor,
    model::{
        base::PageParams,
        teaching_aids::textbooks::{
            CreateAuthorizationRequest, CreateChapterRequest, CreateTeachingAidRequest, ImportProgressResponse, TeachingAid, TeachingAidAuthorizationResponse, TeachingAidChapter, TeachingAidQuery,
            TeachingAidStatsResponse, Textbook, UpdateTeachingAidRequest, RecentTeachingAidsQuery,
        },
        textbooks::{TeachingAidChapter2, TeachingAidPopularSubject, TeachingAidRecentImport},
    },
    service::{
        grade::grade_service::GradeService,
        storage::StorageService,
        subject::SubjectService,
    },
    utils::{
        api_response::{responses, ApiResponse},
        error::AppError,
    },
    web_server::AppState,
};

use crate::model::textbooks::{BookJson, ImportTeachingAidRequest, TextbookImportLog};
use crate::service::teaching_aids::teaching_aids_service::TeachingAidsService;
use crate::service::teaching_aids::textbook::chapter_service::ChapterService;
use crate::service::teaching_aids::textbook::textbook_import_service::TextbookImportService;
use axum::extract::DefaultBodyLimit;
use axum::{
    extract::{Multipart, Path, Query, State},
    response::Json,
    routing::{delete, get, post, put},
    Router,
};
use bytes::Bytes;
use chrono::Utc;
use tracing::error;
use tracing::log::info;
use uuid::Uuid;
use anyhow::Result;
use crate::utils::error_handler::AppResult;

pub struct TeachingAidsRouteState {
    pub teaching_aids_service: Arc<TeachingAidsService>,
    pub storage_service: Arc<dyn StorageService>,
    pub subject_service: SubjectService,
    pub grade_service: GradeService,
    pub paper_service: Arc<PaperService>,
    pub textbook_paper_service: Arc<TextbookPaperService>,
}

pub(crate) fn create_router(app_state: &AppState) -> Router {
    let state = Arc::new(TeachingAidsRouteState {
        teaching_aids_service: app_state.teaching_aids_service.clone(),
        storage_service: app_state.storage_service.clone(),
        subject_service: app_state.subject_service.clone(),
        grade_service: app_state.grade_service.clone(),
        paper_service: app_state.paper_service.clone(),
        textbook_paper_service: app_state.textbook_paper_service.clone(),
    });
    Router::new()
        .route("/latest", get(get_recent_teaching_aids_handler))
        .route("/textbook", post(create_textbook_handler))
        .route("/textbook/{id}", delete(delete_textbook_handler))
        .route("/textbook/{id}", put(update_textbook_handler))
        .route("/textbook/{id}", get(get_textbook_handler))
        .route("/textbook/list", get(list_teaching_aids_handler))
        .route("/textbook/{textbook_id}/chapters", get(get_chapters_handler))
        .route("/stats", get(get_teaching_aid_stats_handler))
        .route("/import/{id}", get(get_import_progress_handler))
        .route("/import/history", get(get_import_history_handler))
        .route("/import", post(import_from_zip_handler).layer(DefaultBodyLimit::max(100 * 1024 * 1024)))
        .route("/chapters/{chapter_id}/answer-sheet", get(get_chapter_answer_sheet_id_handler))

        .route("/importTest",post(import_from_zip_handler_test).layer(DefaultBodyLimit::max(100 * 1024 * 1024)))
        .route("/textbook/listTest", get(list_teaching_aids_handler_test))

        .with_state(state)
}

pub async fn get_recent_teaching_aids_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Query(params): Query<RecentTeachingAidsQuery>,
) -> Result<ApiResponse<Vec<TeachingAid>>, AppError> {
    let limit = params.limit.unwrap_or(8);
    
    match state.teaching_aids_service.find_latest_textbooks(limit).await {
        Ok(textbooks) => {
            let mut aids: Vec<TeachingAid> = textbooks
                .into_iter()
                .map(|textbook| TeachingAid {
                    id: textbook.id,
                    title: textbook.title,
                    description: None,
                    author: None,
                    publisher: textbook.publisher,
                    subject: textbook.subject,
                    grade_level: textbook.grade_level,
                    version: textbook.version,
                    status: textbook.status,
                    cover_path: textbook.cover_path,
                    creator_id: textbook.creator_id,
                    created_at: textbook.created_at,
                    updated_at: textbook.updated_at,
                })
                .collect();
            
            // 按创建时间排序（最新的在前）
            aids.sort_by(|a, b| {
                match (a.created_at, b.created_at) {
                    (Some(a_time), Some(b_time)) => b_time.cmp(&a_time),
                    (Some(_), None) => std::cmp::Ordering::Less,
                    (None, Some(_)) => std::cmp::Ordering::Greater,
                    (None, None) => std::cmp::Ordering::Equal,
                }
            });
            
            // 限制返回数量
            aids.truncate(limit as usize);
            
            Ok(ApiResponse::success(aids, None))
        }
        Err(err) => {
            error!("Failed to get recent teaching aids: {}", err);
            Err(AppError::InternalServerError("Failed to get recent teaching aids".to_string()))
        }
    }
}

pub async fn create_textbook_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    AuthExtractor(auth_context): AuthExtractor,
    Json(request): Json<CreateTeachingAidRequest>,
) -> Result<ApiResponse<TeachingAid>, AppError> {
    // Convert request to Textbook
    let textbook = Textbook {
        id: Uuid::new_v4(),
        title: request.title,
        subject_id: request.subject,
        grade_level_id: request.grade_level,
        publisher: request.publisher,
        publication_year: None,
        cover_path: None,
        isbn: None,
        version: request.version,
        status: Some("draft".to_string()),
        creator_id: Some(auth_context.user_id),
        created_at: Some(Utc::now()),
        updated_at: Some(Utc::now()),
    };

    match state.teaching_aids_service.create_textbook(textbook).await {
        Ok(textbook) => {
            let subject = state
                .subject_service
                .get_subject_by_id(textbook.subject_id.unwrap_or(Uuid::new_v4()))
                .await
                .map(|op| op.map(|subject| subject.name))
                .unwrap_or(None);

            let grade_level = state
                .grade_service
                .get_grade_by_id(textbook.grade_level_id.unwrap_or(Uuid::new_v4()))
                .await
                .map(|op| op.map(|grade| grade.name))
                .unwrap_or(None);

            let aid = TeachingAid {
                id: textbook.id,
                title: textbook.title,
                description: request.description,
                author: request.author,
                publisher: textbook.publisher,
                subject,
                grade_level,
                version: textbook.version,
                status: textbook.status,
                cover_path: textbook.cover_path,
                creator_id: textbook.creator_id,
                created_at: textbook.created_at,
                updated_at: textbook.updated_at,
            };
            Ok(responses::success(aid, Some("Textbook created successfully")))
        }
        Err(e) => {
            error!("Error creating textbook: {}", e);
            Err(AppError::InternalServerError("Failed to create textbook".to_string()))
        }
    }
}

pub async fn import_from_zip_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    AuthExtractor(auth_context): AuthExtractor,
    mut multipart: Multipart,
) -> Result<ApiResponse<TextbookImportLog>, AppError> {
    let mut file_data: Option<Bytes> = None;
    let mut file_name = String::new();
    let mut metadata: Option<ImportTeachingAidRequest> = None;

    // 这部分不能够放在spawn里面
    // 因为handler先返回响应，再在tokio::spawn里异步读取multipart流，但是请求的body在响应返回后，可能已被提前drop了
    // 这会导致：400 Bad Request
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        error!("Failed to read multipart file: {}, {:?}", e.status(), e);
        AppError::BadRequest("Failed to read multipart file".to_string())
    })? {
        let name = field.name().unwrap_or("").to_string();
        if name == "file" {
            file_name = field.file_name().unwrap_or("default_file_name").to_string();
            match field.bytes().await {
                Ok(bytes) => {
                    file_data = Some(bytes);
                }
                Err(err) => {
                    error!("Failed to read multipart field(file): {}", err);
                }
            }
        } else if name == "metadata" {
            match field.bytes().await {
                Ok(data) => {
                    metadata = serde_json::from_slice(&data).unwrap_or(None);
                }
                Err(err) => {
                    error!("Failed to read multipart field(metadata): {}", err);
                }
            }
        }
    }

    tokio::spawn(async move {
        info!("enter tokio::spawn");
        match file_data.ok_or_else(|| AppError::BadRequest("参数解析失败".to_string())) {
            Ok(file_data) => {
                match TextbookImportService::import_textbook_from_zip(state, &auth_context, file_data, file_name, metadata).await {
                    Ok(log) => {
                        info!("✅ 导入成功: {} (章节: {}, 习题: {})", log.filename, log.total_chapters, log.total_exercises);
                    }
                    Err(err) => {
                        error!("❌ 导入失败: {:?}", err);
                    }
                }
            },
            Err(err) => {
                error!("file_data解析失败: {}", err);
            }
        }
    });
    Ok(ApiResponse::success(TextbookImportLog {
        id: Uuid::new_v4(),
        filename: "".to_string(),
        status: "importing".to_string(),
        total_chapters: 0,
        total_exercises: 0,
        started_at: Utc::now(),
        completed_at: Utc::now(),
        created_at: Utc::now(),
    }, Some("导入教辅中".to_string())))
}

pub async fn import_from_zip_handler_test(
    State(state): State<Arc<TeachingAidsRouteState>>,
    mut multipart: Multipart,
)->Result<(),AppError>{
    let mut file_data: Option<Bytes> = None;
    let mut file_name = String::new();
    let mut metadata: Option<ImportTeachingAidRequest> = None;

    while let Some(field) = multipart.next_field().await.map_err(|e| {
        error!("Failed to read multipart file: {}, {:?}", e.status(), e);
        AppError::BadRequest("Failed to read multipart file".to_string())
    })? {
        let name = field.name().unwrap_or("").to_string();
        if name == "file" {
            file_name = field.file_name().unwrap_or("default_file_name").to_string();
            match field.bytes().await {
                Ok(bytes) => {
                    file_data = Some(bytes);
                }
                Err(err) => {
                    error!("Failed to read multipart field(file): {}", err);
                }
            }
        } else if name == "metadata" {
            match field.bytes().await {
                Ok(data) => {
                    metadata = serde_json::from_slice(&data).unwrap_or(None);
                }
                Err(err) => {
                    error!("Failed to read multipart field(metadata): {}", err);
                }
            }
        }
    }


    tokio::spawn(async move {
        info!("enter tokio::spawn");
        match file_data.ok_or_else(|| AppError::BadRequest("参数解析失败".to_string())) {
            Ok(file_data) => {
                match TextbookImportService::import_textbook_from_zip_test(state,file_data, file_name, metadata).await {
                    Ok(()) => {
                        info!("✅ 导入成功");
                        // info!("✅ 导入成功: {} (章节: {}, 习题: {})", log.filename, log.total_chapters, log.total_exercises);
                    }
                    Err(err) => {
                        error!("❌ 导入失败: {:?}", err);
                    }
                }
            },
            Err(err) => {
                error!("file_data解析失败: {}", err);
            }
        }
    });

    Ok(())
}

pub async fn get_textbook_handler(State(state): State<Arc<TeachingAidsRouteState>>, Path(id): Path<Uuid>) -> Result<ApiResponse<TeachingAid>, AppError> {
    match state.teaching_aids_service.get_textbook(id).await {
        Ok(textbook) => {
            let aid = TeachingAid {
                id: textbook.id,
                title: textbook.title,
                description: None, // Not available in textbook model
                author: None,      // Not available in textbook model
                publisher: textbook.publisher,
                subject: textbook.subject,
                grade_level: textbook.grade_level,
                version: textbook.version,
                status: textbook.status,
                cover_path: textbook.cover_path,
                creator_id: textbook.creator_id,
                created_at: textbook.created_at,
                updated_at: textbook.updated_at,
            };
            Ok(ApiResponse::success(aid, None))
        }
        Err(e) => {
            eprintln!("Error getting teaching aid: {}", e);
            Err(AppError::InternalServerError("Failed to get textbook".to_string()))
        }
    }
}

pub async fn list_teaching_aids_handler(State(state): State<Arc<TeachingAidsRouteState>>, Query(params): Query<TeachingAidQuery>) -> Result<ApiResponse<Vec<TeachingAid>>, AppError> {
    let page_params = PageParams {
        page: Some(params.page.unwrap_or(1)),
        page_size: Some(params.page_size.unwrap_or(20)),
    };

    match state.teaching_aids_service.get_textbooks().await {
        Ok(textbooks) => {
            let aids: Vec<TeachingAid> = textbooks
                .into_iter()
                .map(|textbook| TeachingAid {
                    id: textbook.id,
                    title: textbook.title,
                    description: None,
                    author: None,
                    publisher: textbook.publisher,
                    subject: textbook.subject,
                    grade_level: textbook.grade_level,
                    version: textbook.version,
                    status: textbook.status,
                    cover_path: textbook.cover_path,
                    creator_id: textbook.creator_id,
                    created_at: textbook.created_at,
                    updated_at: textbook.updated_at,
                })
                .collect();
            Ok(ApiResponse::success(aids, None))
        }
        Err(err) => {
            error!("Failed to list textbooks: {}", err);
            Err(AppError::InternalServerError("Failed to list textbooks".to_string()))
        }
    }
}

pub async fn list_teaching_aids_handler_test(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Query(params): Query<TeachingAidQuery>,
)->Result<ApiResponse<()>,AppError>{

    match state.teaching_aids_service.get_textbooks_test(params).await {
        Ok(_) => {}
        Err(_) => {}
    }


    Ok((ApiResponse::success((), None)))
}

pub async fn update_textbook_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Path(aid_id): Path<Uuid>,
    Json(request): Json<UpdateTeachingAidRequest>,
) -> Result<ApiResponse<TeachingAid>, AppError> {
    // Get existing textbook first
    match state.teaching_aids_service.get_textbook(aid_id).await {
        Ok(mut textbook) => {
            // Update fields if provided
            if let Some(title) = request.title {
                textbook.title = title;
            }
            if let Some(publisher) = request.publisher {
                textbook.publisher = Some(publisher);
            }
            if let Some(version) = request.version {
                textbook.version = Some(version);
            }
            if let Some(status) = request.status {
                textbook.status = Some(status);
            }
            // if let Some(author) = request.author {
            //     textbook.author = Some(author);
            // }
            // if let Some(description) = &request.description {
            //     textbook.description = Some(description.clone());
            // }
            if let Some(subject_id) = request.subject {
                textbook.subject_id = Some(subject_id);
            }
            if let Some(grade_level_id) = request.grade_level {
                textbook.grade_level_id = Some(grade_level_id);
            }

            textbook.updated_at = Some(chrono::Utc::now());

            match state.teaching_aids_service.update_textbook(textbook.id, textbook.clone().into()).await {
                Ok(_) => {
                    let aid = TeachingAid {
                        id: textbook.id,
                        title: textbook.title,
                        description: request.description,
                        author: None,
                        publisher: textbook.publisher,
                        subject: textbook.subject,
                        grade_level: textbook.grade_level,
                        version: textbook.version,
                        status: textbook.status,
                        cover_path: textbook.cover_path,
                        creator_id: textbook.creator_id,
                        created_at: textbook.created_at,
                        updated_at: textbook.updated_at,
                    };
                    Ok(ApiResponse::success(aid, None))
                }
                Err(e) => {
                    eprintln!("Error updating textbook: {}", e);
                    Err(AppError::InternalServerError("Failed to update textbook".to_string()))
                }
            }
        }
        Err(e) => {
            eprintln!("Error finding textbook: {}", e);
            Err(AppError::InternalServerError("textbook not found".to_string()))
        }
    }
}

pub async fn delete_textbook_handler(State(state): State<Arc<TeachingAidsRouteState>>, Path(aid_id): Path<Uuid>) -> Result<ApiResponse<()>, AppError> {
    match state.teaching_aids_service.delete_textbook(aid_id).await {
        Ok(_) => Ok(ApiResponse::success((), None)),
        Err(e) => {
            eprintln!("Error deleting teaching aid: {}", e);
            Err(AppError::InternalServerError("Failed to delete textbook".to_string()))
        }
    }
}

pub async fn create_chapter_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Path(aid_id): Path<Uuid>,
    Json(request): Json<CreateChapterRequest>,
) -> Result<ApiResponse<TeachingAidChapter>, AppError> {
    let chapter = TeachingAidChapter {
        id: Uuid::new_v4(),
        textbook_id: aid_id,
        chapter_number: request.chapter_number,
        title: request.title,
        content: request.content,
        knowledge_points: request.knowledge_points,
        created_at: chrono::Utc::now(),
    };

    // For now, just return the chapter as if it was created
    // TODO: Implement actual chapter creation in service
    Ok(ApiResponse::success(chapter, Some("Chapter created successfully".to_string())))
}

pub async fn get_chapters_handler(State(state): State<Arc<TeachingAidsRouteState>>, Path(textbook_id): Path<Uuid>) -> Result<ApiResponse<Vec<TeachingAidChapter2>>, AppError> {
    match state.teaching_aids_service.get_chapters_by_textbook_id(textbook_id).await {
        Ok(chapters) => Ok(ApiResponse::success(chapters, None)),
        Err(err) => {
            error!("Failed to get chapters, textbook_id is {}: {}", textbook_id, err);
            Err(AppError::InternalServerError(format!("Failed to get chapters, textbook_id is {}", textbook_id)))
        }
    }
}

pub async fn get_teaching_aid_stats_handler(State(_state): State<Arc<TeachingAidsRouteState>>) -> Result<ApiResponse<TeachingAidStatsResponse>, AppError> {
    let popular_subjects = vec![
        TeachingAidPopularSubject {
            subject: "物理".to_string(),
            count: 1,
        },
        TeachingAidPopularSubject {
            subject: "数学".to_string(),
            count: 1,
        },
    ];

    let recent_imports = vec![
        TeachingAidRecentImport {
            id: "1".to_string(),
            title: "xxx练习册".to_string(),
            status: "draft".to_string(),
            count: 1,
            imported_at: Utc::now(),
        },
        TeachingAidRecentImport {
            id: "2".to_string(),
            title: "yyy练习册".to_string(),
            status: "published".to_string(),
            count: 1,
            imported_at: Utc::now(),
        },
    ];

    let stats = TeachingAidStatsResponse {
        total_textbooks: 2,
        total_chapters: 10,
        total_exercises: 60,
        total_authorized_tenants: 1,
        popular_subjects: Some(popular_subjects),
        recent_imports: Some(recent_imports),
    };
    Ok(ApiResponse::success(stats, None))
}

pub async fn get_chapter_handler(State(_state): State<Arc<TeachingAidsRouteState>>, Path(chapter_id): Path<Uuid>) -> Result<ApiResponse<TeachingAidChapter>, AppError> {
    // Return mock chapter for now
    let chapter = TeachingAidChapter {
        id: chapter_id,
        textbook_id: Uuid::new_v4(),
        chapter_number: 1,
        title: "Mock Chapter".to_string(),
        content: Some("Mock content".to_string()),
        knowledge_points: Some(serde_json::json!({})),
        created_at: chrono::Utc::now(),
    };
    Ok(ApiResponse::success(chapter, None))
}

pub async fn get_chapter_answer_sheet_id_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Path(chapter_id): Path<Uuid>,
) -> Result<ApiResponse<Option<String>>, AppError> {
    match ChapterService::get_answer_sheet_id_by_chapter_id(state, chapter_id).await {
        Ok(answer_sheet_id) => {
            Ok(ApiResponse::success(answer_sheet_id, None))
        }
        Err(e) => {
            error!("获取章节答题卡ID失败, chapter_id: {}, error: {}", chapter_id, e);
            Err(AppError::InternalServerError("获取答题卡ID失败".to_string()))
        }
    }
}
pub async fn delete_chapter_handler(State(_state): State<Arc<TeachingAidsRouteState>>, Path(_chapter_id): Path<Uuid>) -> Result<ApiResponse<()>, AppError> {
    // Mock deletion for now
    Ok(ApiResponse::success((), Some("Chapter deleted successfully".to_string())))
}

pub async fn create_authorization_handler(
    State(_state): State<Arc<TeachingAidsRouteState>>,
    Json(request): Json<CreateAuthorizationRequest>,
) -> Result<ApiResponse<TeachingAidAuthorizationResponse>, AppError> {
    // For now, just return success since the service method doesn't exist
    // TODO: Implement proper authorization creation
    let auth = TeachingAidAuthorizationResponse {
        id: Uuid::new_v4(),
        textbook_id: request.textbook_id,
        tenant_id: request.tenant_id,
        granted_by: Uuid::new_v4(),
        created_at: chrono::Utc::now(),
    };
    Ok(ApiResponse::success(auth, None))
}

pub async fn get_authorizations_handler(State(_state): State<Arc<TeachingAidsRouteState>>, Path(_aid_id): Path<Uuid>) -> Result<ApiResponse<Vec<TeachingAidAuthorizationResponse>>, AppError> {
    // Return empty list for now
    let auths: Vec<TeachingAidAuthorizationResponse> = vec![];
    Ok(ApiResponse::success(auths, None))
}

pub async fn delete_authorization_handler(State(_state): State<Arc<TeachingAidsRouteState>>, Path(_auth_id): Path<Uuid>) -> Result<ApiResponse<()>, AppError> {
    // Mock deletion for now
    Ok(ApiResponse::success((), Some("Authorization deleted successfully".to_string())))
}

pub async fn get_import_progress_handler(State(state): State<Arc<TeachingAidsRouteState>>, Path(import_id): Path<Uuid>) -> Result<ApiResponse<ImportProgressResponse>, AppError> {
    let progress = ImportProgressResponse {
        id: import_id,
        filename: "mock_import.xlsx".to_string(),
        status: "completed".to_string(),
        total_records: 100,
        processed_records: 100,
        success_count: 95,
        error_count: 5,
        started_at: Utc::now(),
        completed_at: Some(Utc::now()),
    };
    Ok(ApiResponse::success(progress, None))
}

pub async fn get_import_history_handler(State(state): State<Arc<TeachingAidsRouteState>>, Query(_params): Query<PageParams>) -> Result<ApiResponse<Vec<ImportProgressResponse>>, AppError> {
    // Return empty history for now
    let history: Vec<ImportProgressResponse> = vec![
        ImportProgressResponse {
            id: Uuid::new_v4(),
            filename: "mock_import.xlsx".to_string(),
            status: "completed".to_string(),
            total_records: 100,
            processed_records: 100,
            success_count: 95,
            error_count: 5,
            started_at: Utc::now(),
            completed_at: Some(Utc::now()),
        },
        ImportProgressResponse {
            id: Uuid::new_v4(),
            filename: "mock_import.xlsx".to_string(),
            status: "completed".to_string(),
            total_records: 100,
            processed_records: 100,
            success_count: 95,
            error_count: 5,
            started_at: Utc::now(),
            completed_at: Some(Utc::now()),
        },
    ];
    Ok(ApiResponse::success(history, None))
}
