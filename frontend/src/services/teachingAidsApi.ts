import apiClient from './apiClient';

const API_BASE_URL = '/api/v1';

// Types
export interface TeachingAid {
  id: string;
  title: string;
  description: string;
  author: string;
  publisher: string;
  subject: string;
  grade_level: string;
  content_type: string;
  version: string;
  status: string;
  coverPath: string;
  created_at: string;
  updated_at: string;
}

export interface TeachingAidChapter {
  id: string;
  teaching_aid_id: string;
  title: string;
  description: string;
  chapter_number: number;
  difficulty_level: string;
  created_at: string;
}

export interface TeachingAidChapter2 {
  id: string,
  textbook_id: string,
  parent_id: string,
  chapter_number: number,
  title: string,
  description: string,
  content: any,
  metadata: any,
  creator_id: string,
  created_at: string,
  updated_at: string,
}

export interface TeachingAidExercise {
  id: string;
  teaching_aid_id: string;
  chapter_id?: string;
  question_type: string;
  question_content: string;
  difficulty_level: string;
  points: number;
  created_at: string;
}

export interface TeachingAidStats {
  total_textbooks: number;
  total_chapters: number;
  total_exercises: number;
  total_authorized_tenants: number;
  popular_subjects: Array<{
    subject: string;
    count: number;
  }>;
  recent_imports: Array<{
    id: string;
    title: string;
    status: string;
    count: number;
    imported_at: string;
  }>;
}

export interface ImportProgress {
  id: string;
  filename: string;
  total_records: number;
  processed_records: number;
  success_count: number;
  error_count: number;
  status: string;
  started_at: string;
  completed_at?: string;
}

export interface TeachingAidAuthorization {
  id: string;
  teaching_aid_id: string;
  tenant_id: string;
  tenant_name: string;
  access_level: string;
  status: string;
  authorized_at: string;
  expires_at?: string;
}

export interface TeachingAidVersion {
  id: string;
  teaching_aid_id: string;
  version: string;
  changes: string;
  created_at: string;
}

export interface CreateTeachingAidRequest {
  title: string;
  description: string;
  author: string;
  publisher: string;
  subject: string;
  grade_level: string;
  content_type: string;
}

export interface ImportTeachingAidRequest {
  file: File;
  metadata: {
    title: string;
    description: string;
    author: string;
    publisher: string;
    subject: string;
    grade_level: string;
    content_type: string;
    import_exercises: boolean;
  }
}

export interface ImportTeachingAidFormParams {
  file: File;
  title: string;
  description: string;
  author: string;
  publisher: string;
  subject: string;
  grade_level: string;
  content_type: string;
  import_exercises: boolean;
}

export interface UpdateAuthorizationRequest {
  tenant_id: string;
  access_level: string;
  expires_at?: string;
}

// API Services
export const teachingAidApi = {
  async getAnswerSheetIdByChapterId(chapterId: string): Promise<{ answerSheetId: string }> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/chapters/${chapterId}/answer-sheet`);
    return response.data;
  },

  async getTeachingAids(): Promise<TeachingAid[]> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/textbook/list`);
    return response.data;
  },

  async getTeachingAid(id: string): Promise<TeachingAid> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/textbook/${id}`);
    return response.data;
  },

  async createTeachingAid(data: CreateTeachingAidRequest): Promise<TeachingAid> {
    const response = await apiClient.post(`${API_BASE_URL}/teaching-aids/textbook`, data);
    return response.data;
  },

  async updateTeachingAid(id: string, data: Partial<CreateTeachingAidRequest>): Promise<TeachingAid> {
    const response = await apiClient.put(`${API_BASE_URL}/teaching-aids/textbook/${id}`, data);
    return response.data;
  },

  async deleteTeachingAid(id: string): Promise<void> {
    await apiClient.delete(`${API_BASE_URL}/teaching-aids/textbook/${id}`);
  },

  async getTeachingAidStats(): Promise<TeachingAidStats> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/stats`);
    return response.data;
  }
};

export const chapterApi = {
  async getChapters(teachingAidId: string): Promise<TeachingAidChapter2[]> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/textbook/${teachingAidId}/chapters`);
    return response.data;
  },

  async createChapter(teachingAidId: string, data: Omit<TeachingAidChapter, 'id' | 'teaching_aid_id' | 'created_at'>): Promise<TeachingAidChapter> {
    const response = await apiClient.post(`${API_BASE_URL}/teaching-aids/${teachingAidId}/chapters`, data);
    return response.data;
  }
};

export const exerciseApi = {
  async getExercises(params: { teaching_aid_id?: string; chapter_id?: string }): Promise<TeachingAidExercise[]> {
    const response = await apiClient.get(`${API_BASE_URL}/exercises`, { params });
    return response.data;
  }
};

export const importApi = {
  async importTeachingAid(data: ImportTeachingAidRequest): Promise<ImportProgress> {
    const formData = new FormData();
    formData.append('file', data.file);
    formData.append('metadata', JSON.stringify(data.metadata));

    const response = await apiClient.post(`${API_BASE_URL}/teaching-aids/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  },

    async importTeachingAidTest(file:File):Promise<any>{
        const formData = new FormData();
        formData.append('file', file);
        const response = await apiClient.post(`${API_BASE_URL}/teaching-aids/importTest`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return response.data;
    },

  async getImportProgress(id: string): Promise<ImportProgress> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/import/${id}`);
    return response.data;
  },

  async getImportHistory(): Promise<ImportProgress[]> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/import/history`);
    return response.data;
  }
};

export const authorizationApi = {
  async getTeachingAidAuthorizations(teachingAidId: string): Promise<TeachingAidAuthorization[]> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/${teachingAidId}/authorizations`);
    return response.data;
  },

  async updateAuthorization(teachingAidId: string, data: UpdateAuthorizationRequest): Promise<TeachingAidAuthorization> {
    const response = await apiClient.post(`${API_BASE_URL}/teaching-aids/${teachingAidId}/authorizations`, data);
    return response.data;
  }
};

export const versionApi = {
  async getTeachingAidVersions(teachingAidId: string): Promise<TeachingAidVersion[]> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/${teachingAidId}/versions`);
    return response.data;
  }
};

// 工作台教辅数据类型（适配前端组件）
export interface WorkspaceTeachingAid {
  id: string;
  title: string;
  subject: string;
  grade: string;
  type: 'exercise' | 'test' | 'material' | 'video';
  author: string;
  uploadDate: string;
  downloads: number;
  rating: number;
  isNew: boolean;
  thumbnail?: string;
  description: string;
}

// 数据转换函数
function transformTeachingAidToWorkspace(aid: TeachingAid): WorkspaceTeachingAid {
  // 根据content_type映射到前端类型
  const typeMapping: Record<string, WorkspaceTeachingAid['type']> = {
    'exercise': 'exercise',
    'test': 'test',
    'material': 'material',
    'video': 'video',
    'textbook': 'material',
    'document': 'material'
  };

  // 计算是否为新资源（7天内创建的）
  const isNew = new Date(aid.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

  return {
    id: aid.id,
    title: aid.title,
    subject: aid.subject,
    grade: aid.grade_level,
    type: typeMapping[aid.content_type] || 'material',
    author: aid.author,
    uploadDate: aid.created_at.split('T')[0], // 格式化日期
    downloads: Math.floor(Math.random() * 500) + 50, // 模拟下载量，后续可从统计API获取
    rating: Math.round((Math.random() * 2 + 3) * 10) / 10, // 模拟评分 3.0-5.0
    isNew,
    thumbnail: aid.coverPath,
    description: aid.description
  };
}

// 工作台专用API
export const workspaceTeachingAidApi = {
  /**
   * 获取最新教辅资料（用于工作台展示）
   * @param limit 限制数量，默认4个
   */
  async getLatestTeachingAids(limit: number = 4): Promise<WorkspaceTeachingAid[]> {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/latest`, {
        params: {
          limit,
          sort: 'created_at',
          order: 'desc'
        }
      });
      
      const teachingAids: TeachingAid[] = response.data;
      return teachingAids.map(transformTeachingAidToWorkspace);
    } catch (error) {
      console.error('Failed to fetch latest teaching aids:', error);
      throw error;
    }
  },

  /**
   * 获取热门教辅资料
   * @param limit 限制数量，默认4个
   */
  async getPopularTeachingAids(limit: number = 4): Promise<WorkspaceTeachingAid[]> {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/textbook/list`, {
        params: {
          limit,
          sort: 'downloads', // 假设后端支持按下载量排序
          order: 'desc'
        }
      });
      
      const teachingAids: TeachingAid[] = response.data;
      return teachingAids.map(transformTeachingAidToWorkspace);
    } catch (error) {
      // 如果后端不支持按下载量排序，回退到按创建时间排序
      console.warn('Popular sorting not supported, falling back to latest');
      return this.getLatestTeachingAids(limit);
    }
  },

  /**
   * 获取教辅统计信息（用于工作台指标卡片）
   */
  async getTeachingAidMetrics() {
    try {
      const stats = await teachingAidApi.getTeachingAidStats();
      return {
        totalCount: stats.total_textbooks,
        todayNew: stats.recent_imports.filter(item => {
          const importDate = new Date(item.imported_at);
          const today = new Date();
          return importDate.toDateString() === today.toDateString();
        }).length,
        weeklyPopular: stats.popular_subjects.reduce((sum, subject) => sum + subject.count, 0)
      };
    } catch (error) {
      console.error('Failed to fetch teaching aid metrics:', error);
      // 返回默认值
      return {
        totalCount: 456,
        todayNew: 3,
        weeklyPopular: 12
      };
    }
  }
};