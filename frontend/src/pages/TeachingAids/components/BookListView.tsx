import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card.tsx";
import {Label} from "@/components/ui/label.tsx";
import {Edit, Filter, Grid3X3, List, Search, Trash2} from "lucide-react";
import {Input} from "@/components/ui/input.tsx";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select.tsx";
import {Button} from "@/components/ui/button.tsx";
import  {FC, useState} from "react";
import {GradeLevelSummary, SubjectSummary} from "@/types";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table.tsx";
import TeachingAidCardView from "@/pages/TeachingAids/components/TeachingAidCardView.tsx";

type BookListViewProps = {
    subjectOptions:SubjectSummary[],
    gradeLevelOptions:GradeLevelSummary[],
};

const BookListView: FC<BookListViewProps> = ({subjectOptions,gradeLevelOptions}) => {

    const [searchQuery, setSearchQuery] = useState('');
    const [selectedSubject, setSelectedSubject] = useState<string>('');
    const [selectedGradeLevel, setSelectedGradeLevel] = useState<string>('');
    const [selectedStatus, setSelectedStatus] = useState<string>('');

    return (
        <div>
            <Card>
                <CardHeader>
                    <CardTitle>搜索与筛选</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="search">搜索</Label>
                            <div className="relative">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="search"
                                    placeholder="搜索教辅..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="pl-8"
                                />
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Label>学科</Label>
                            <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                                <SelectTrigger>
                                    <SelectValue placeholder="全部学科" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="allSubject">全部学科</SelectItem>
                                    {subjectOptions.filter(s => s.is_active).map((subject) => (
                                        <SelectItem key={subject.code} value={subject.id}>
                                            {subject.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <Label>年级</Label>
                            <Select value={selectedGradeLevel} onValueChange={setSelectedGradeLevel}>
                                <SelectTrigger>
                                    <SelectValue placeholder="全部年级" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="allGrade">全部年级</SelectItem>
                                    {gradeLevelOptions.filter(g => g.is_active).map((grade_level) => (
                                        <SelectItem key={grade_level.code} value={grade_level.id}>
                                            {grade_level.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <Label>状态</Label>
                            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                <SelectTrigger>
                                    <SelectValue placeholder="全部状态" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="allStatus">全部状态</SelectItem>
                                    <SelectItem value="draft">草稿</SelectItem>
                                    <SelectItem value="published">已发布</SelectItem>
                                    <SelectItem value="archived">已归档</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <Label>&nbsp;</Label>
                            <Button
                                variant="outline"
                                onClick={() => {
                                    setSearchQuery('');
                                    setSelectedSubject('');
                                    setSelectedGradeLevel('');
                                    setSelectedStatus('');
                                }}
                            >
                                <Filter className="h-4 w-4 mr-2" />
                                重置
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/*<Card>*/}
            {/*    <CardHeader>*/}
            {/*        <div className="flex items-center justify-between">*/}
            {/*            <div>*/}
            {/*                <CardTitle>教辅列表</CardTitle>*/}
            {/*                <CardDescription>*/}
            {/*                    显示 {filteredTeachingAids.length} 个教辅资源*/}
            {/*                </CardDescription>*/}
            {/*            </div>*/}
            {/*            <div className="flex items-center space-x-2">*/}
            {/*                <Button*/}
            {/*                    variant={viewMode === 'table' ? 'default' : 'outline'}*/}
            {/*                    size="sm"*/}
            {/*                    onClick={() => setViewMode('table')}*/}
            {/*                >*/}
            {/*                    <List className="mr-2 h-4 w-4" />*/}
            {/*                    列表*/}
            {/*                </Button>*/}
            {/*                <Button*/}
            {/*                    variant={viewMode === 'card' ? 'default' : 'outline'}*/}
            {/*                    size="sm"*/}
            {/*                    onClick={() => setViewMode('card')}*/}
            {/*                >*/}
            {/*                    <Grid3X3 className="mr-2 h-4 w-4" />*/}
            {/*                    卡片*/}
            {/*                </Button>*/}
            {/*            </div>*/}
            {/*        </div>*/}
            {/*    </CardHeader>*/}
            {/*    <CardContent>*/}
            {/*        {viewMode === 'table' ? (*/}
            {/*            <Table>*/}
            {/*                <TableHeader>*/}
            {/*                    <TableRow>*/}
            {/*                        <TableHead>标题</TableHead>*/}
            {/*                        <TableHead>作者</TableHead>*/}
            {/*                        <TableHead>学科</TableHead>*/}
            {/*                        <TableHead>年级</TableHead>*/}
            {/*                        <TableHead>内容类型</TableHead>*/}
            {/*                        <TableHead>版本</TableHead>*/}
            {/*                        <TableHead>状态</TableHead>*/}
            {/*                        <TableHead>操作</TableHead>*/}
            {/*                    </TableRow>*/}
            {/*                </TableHeader>*/}
            {/*                <TableBody>*/}
            {/*                    {filteredTeachingAids.map((aid) => (*/}
            {/*                        <TableRow key={aid.id}>*/}
            {/*                            <TableCell className="font-medium cursor-pointer" onClick={() => openPreviewPage(aid)}>{aid.title}</TableCell>*/}
            {/*                            <TableCell>{aid.author}</TableCell>*/}
            {/*                            <TableCell>{aid.subject}</TableCell>*/}
            {/*                            <TableCell>{aid.grade_level}</TableCell>*/}
            {/*                            <TableCell>{getContentTypeBadge(aid.content_type)}</TableCell>*/}
            {/*                            <TableCell>{aid.version}</TableCell>*/}
            {/*                            <TableCell>{getStatusBadge(aid.status)}</TableCell>*/}
            {/*                            <TableCell>*/}
            {/*                                <div className="flex gap-1">*/}
            {/*                                    <Button variant="ghost" size="sm" onClick={() => {*/}
            {/*                                        setIsUpdateDialogOpen(true);*/}
            {/*                                        setUpdatingTextbookId(aid.id);*/}
            {/*                                    }}>*/}
            {/*                                        <Edit className="h-4 w-4" />*/}
            {/*                                    </Button>*/}
            {/*                                    <Button variant="ghost" size="sm" onClick={() => handleDeleteTeachingAid(aid.id)}>*/}
            {/*                                        <Trash2 className="h-4 w-4" />*/}
            {/*                                    </Button>*/}
            {/*                                </div>*/}
            {/*                            </TableCell>*/}
            {/*                        </TableRow>*/}
            {/*                    ))}*/}
            {/*                </TableBody>*/}
            {/*            </Table>*/}
            {/*        ) : (*/}
            {/*            <TeachingAidCardView*/}
            {/*                aids={filteredTeachingAids}*/}
            {/*                onPreview={openPreviewPage}*/}
            {/*                onEdit={(aid) => {*/}
            {/*                    setIsUpdateDialogOpen(true);*/}
            {/*                    setUpdatingTextbookId(aid.id);*/}
            {/*                }}*/}
            {/*                onDelete={handleDeleteTeachingAid}*/}
            {/*                renderStatusBadge={getStatusBadge}*/}
            {/*                renderContentTypeBadge={getContentTypeBadge}*/}
            {/*            />*/}
            {/*        )}*/}
            {/*    </CardContent>*/}
            {/*</Card>*/}
        </div>
    );
};

export default BookListView;
